<template>
  <el-dialog
    :visible.sync="localVisible"
    :title="$T('编辑站点')"
    width="800px"
    :close-on-click-modal="false"
    class="edit-site-dialog"
    @close="handleClose"
  >
    <div class="dialog-content bg-BG1 p-J3">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-text text-T2">{{ $T("加载中...") }}</div>
      </div>

      <!-- 编辑表单 -->
      <div v-else-if="siteData" class="form-section">
        <el-form
          ref="editForm"
          :model="formData"
          :rules="formRules"
          label-position="top"
          size="small"
        >
          <!-- 站点名称和站点类型 -->
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="$T('站点名称')" prop="site_name">
                <el-input
                  v-model="formData.site_name"
                  :placeholder="$T('请输入站点名称')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('站点类型')" prop="site_type">
                <el-input
                  v-model="siteTypeName"
                  :disabled="true"
                  :placeholder="$T('站点类型')"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 经度和纬度 -->
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="$T('经度')" prop="longitude">
                <el-input
                  v-model="formData.longitude"
                  :placeholder="$T('请输入经度')"
                >
                  <template #append>
                    <el-button
                      v-if="isLoadBaiduMapResource"
                      type="primary"
                      icon="el-icon-paperclip"
                      @click="openMapDialog"
                    ></el-button>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('纬度')" prop="latitude">
                <el-input
                  v-model="formData.latitude"
                  :placeholder="$T('请输入纬度')"
                >
                  <template #append>
                    <el-button
                      v-if="isLoadBaiduMapResource"
                      type="primary"
                      icon="el-icon-paperclip"
                      @click="openMapDialog"
                    ></el-button>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 储能类特有字段 -->
          <template v-if="currentGroup === 'STORAGE'">
            <el-row :gutter="16">
              <!-- 电压等级 -->
              <el-col :span="12">
                <el-form-item :label="$T('电压等级')" prop="voltage_level">
                  <el-select
                    v-model="formData.voltage_level"
                    :placeholder="$T('请选择')"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(value, key) in VOLTAGE_LEVEL"
                      :key="value"
                      :label="key"
                      :value="value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>

              <!-- 并网电压 -->
              <el-col :span="12">
                <el-form-item :label="$T('并网电压')" prop="grid_voltage">
                  <el-input
                    v-model="formData.grid_voltage"
                    :placeholder="$T('请输入数值')"
                    type="number"
                  >
                    <template slot="append">kV</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <!-- 总装机容量 -->
              <el-col :span="12">
                <el-form-item :label="$T('总装机容量')" prop="total_capacity">
                  <el-input
                    v-model="formData.total_capacity"
                    :placeholder="$T('请输入数值')"
                    type="number"
                  >
                    <template slot="append">kWh</template>
                  </el-input>
                </el-form-item>
              </el-col>

              <!-- 总储电量 -->
              <el-col :span="12">
                <el-form-item :label="$T('总储电量')" prop="total_storage">
                  <el-input
                    v-model="formData.total_storage"
                    :placeholder="$T('请输入数值')"
                    type="number"
                  >
                    <template slot="append">kWh</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </template>

          <!-- 新能源类特有字段 -->
          <template v-if="currentGroup === 'RENEWABLE'">
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item :label="$T('电压等级')" prop="voltage_level">
                  <el-select
                    v-model="formData.voltage_level"
                    :placeholder="$T('请选择')"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(value, key) in VOLTAGE_LEVEL"
                      :key="value"
                      :label="key"
                      :value="value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$T('发电模式')" prop="generation_mode">
                  <el-select
                    v-model="formData.generation_mode"
                    :placeholder="$T('请选择')"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in generationModeOptions"
                      :key="item.value"
                      :label="$T(item.label)"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item :label="$T('并网电压')" prop="grid_voltage">
                  <el-input
                    v-model="formData.grid_voltage"
                    :placeholder="$T('请输入数值')"
                    style="width: 100%"
                  >
                    <template slot="append">kV</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$T('总装机容量')" prop="total_capacity">
                  <el-input
                    v-model="formData.total_capacity"
                    :placeholder="$T('请输入数值')"
                    style="width: 100%"
                  >
                    <template slot="append">kWh</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </template>

          <!-- 其他类特有字段 -->
          <template v-if="currentGroup === 'OTHER'">
            <!-- 第3行：电压等级 + 发电模式 -->
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item :label="$T('电压等级')" prop="voltage_level">
                  <el-select
                    v-model="formData.voltage_level"
                    :placeholder="$T('请选择')"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="(value, key) in VOLTAGE_LEVEL"
                      :key="value"
                      :label="key"
                      :value="value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$T('发电模式')" prop="generation_mode">
                  <el-select
                    v-model="formData.generation_mode"
                    :placeholder="$T('请选择')"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in generationModeOptions"
                      :key="item.value"
                      :label="$T(item.label)"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 第4行：并网电压 + 总装机容量 -->
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item :label="$T('并网电压')" prop="grid_voltage">
                  <el-input
                    v-model="formData.grid_voltage"
                    :placeholder="$T('请输入数值')"
                    style="width: 100%"
                  >
                    <template slot="append">kV</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="$T('总装机容量')" prop="total_capacity">
                  <el-input
                    v-model="formData.total_capacity"
                    :placeholder="$T('请输入数值')"
                    style="width: 100%"
                  >
                    <template slot="append">kWh</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </template>

          <!-- 第5行：投运时间 + 联系人 -->
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="$T('投运时间')" prop="operation_date">
                <el-date-picker
                  v-model="formData.operation_date"
                  type="date"
                  :placeholder="$T('请选择日期')"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('联系人')" prop="contact_person">
                <el-input
                  v-model="formData.contact_person"
                  :placeholder="$T('请输入内容')"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第6行：联系电话 + 关联站点 -->
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="$T('联系电话')" prop="phone_number">
                <el-input
                  v-model="formData.phone_number"
                  :placeholder="$T('请输入内容')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('关联站点')" prop="related_room">
                <el-input
                  v-model="selectedRoomName"
                  :placeholder="$T('请选择')"
                  readonly
                  style="width: 100%"
                >
                  <el-button
                    slot="suffix"
                    type="text"
                    icon="el-icon-paperclip"
                    @click="openRoomSelector"
                    class="room-selector-btn"
                  />
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 第7行：地址 -->
          <el-row>
            <el-col :span="24">
              <el-form-item :label="$T('地址')" prop="site_address">
                <el-input
                  v-model="formData.site_address"
                  :placeholder="$T('请输入内容')"
                  type="textarea"
                  :rows="2"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 图片上传 -->
          <el-row>
            <el-col :span="24">
              <el-form-item :label="$T('站点图片')" prop="imageUrl">
                <div class="image-upload-container">
                  <!-- 图片预览 -->
                  <div v-if="formData.imageUrl" class="image-preview">
                    <img
                      :src="getImageDisplayUrl(formData.imageUrl)"
                      class="preview-image"
                      @click="previewImage"
                    />
                    <div class="image-actions">
                      <el-button
                        type="text"
                        icon="el-icon-view"
                        @click="previewImage"
                      >
                        {{ $T("预览") }}
                      </el-button>
                      <el-button
                        type="text"
                        icon="el-icon-delete"
                        @click="removeImage"
                      >
                        {{ $T("删除") }}
                      </el-button>
                    </div>
                  </div>

                  <!-- 上传按钮 -->
                  <el-upload
                    v-else
                    class="image-uploader"
                    action="#"
                    :show-file-list="false"
                    :before-upload="handleImageUpload"
                    accept="image/*"
                  >
                    <div class="upload-placeholder">
                      <i class="el-icon-plus"></i>
                      <div class="upload-text">{{ $T("上传图片") }}</div>
                    </div>
                  </el-upload>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <!-- 没有数据时显示 -->
      <div v-else class="no-data-container">
        <div class="no-data-text text-T2">{{ $T("没有站点数据") }}</div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">{{ $T("取消") }}</el-button>
      <el-button type="primary" @click="handleSave">{{ $T("保存") }}</el-button>
    </div>

    <!-- 站点选择弹窗 -->
    <el-dialog
      :visible.sync="roomSelectorVisible"
      :title="$T('选择关联站点')"
      width="600px"
      :close-on-click-modal="false"
      :modal-append-to-body="false"
      :append-to-body="true"
      class="room-selector-dialog"
    >
      <div class="room-selector-content">
        <!-- 站点类型筛选 -->
        <div class="filter-section mb-J3">
          <div class="filter-label text-T1 font-medium mb-J1">
            {{ $T("站点类型") }}
          </div>
          <el-select
            v-model="selectedRoomType"
            :placeholder="
              roomTypesLoading ? $T('加载中...') : $T('请选择站点类型')
            "
            :loading="roomTypesLoading"
            :disabled="roomTypesLoading || availableRoomTypes.length === 0"
            @change="handleRoomTypeChange"
            clearable
            filterable
            style="width: 100%; max-width: 300px"
          >
            <!-- 提示信息 -->
            <div v-if="availableRoomTypes.length > 1" class="select-tip">
              <i class="el-icon-info"></i>
              {{ $T("共有") }} {{ availableRoomTypes.length }}
              {{ $T("种站点类型可选") }}
            </div>
            <el-option
              v-for="type in availableRoomTypes"
              :key="type.value"
              :label="type.label"
              :value="type.value"
              :disabled="type.disabled"
            >
              <span>{{ type.label }}</span>
              <span v-if="type.count !== undefined" class="room-count">
                ({{ type.count }}{{ $T("个站点") }})
              </span>
            </el-option>
            <div
              v-if="availableRoomTypes.length === 0 && !roomTypesLoading"
              class="no-data-tip"
            >
              <i class="el-icon-info"></i>
              {{ $T("暂无可用的站点类型") }}
            </div>
          </el-select>
        </div>

        <!-- 站点列表 -->
        <div class="room-list" v-loading="roomListLoading">
          <el-table
            :data="filteredRooms"
            @selection-change="handleRoomSelectionChange"
            height="300px"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column
              prop="name"
              :label="$T('站点名称')"
              show-overflow-tooltip
              min-width="120"
            />
            <el-table-column
              :label="$T('站点类型')"
              show-overflow-tooltip
              min-width="100"
            >
              <template slot-scope="scope">
                {{ getRoomTypeName(scope.row.roomtype) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="id"
              :label="$T('站点ID')"
              show-overflow-tooltip
              min-width="80"
            />
          </el-table>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="roomSelectorVisible = false">
          {{ $T("取消") }}
        </el-button>
        <el-button type="primary" @click="confirmRoomSelection">
          {{ $T("确定") }}
        </el-button>
      </div>
    </el-dialog>

    <MapPointDialog
      v-model="showMapDialog"
      @confirm="handleLocationConfirm"
    ></MapPointDialog>
  </el-dialog>
</template>

<script>
import { getEnumLabel, getEnumOptions } from "@/utils/enumManager";
import { getSiteById, updateSite } from "@/api/site-management";
import { getAvailableRooms, uploadImage } from "@/api/base-config";
import MapPointDialog from "@/components/MapPointDialog.vue";

export default {
  name: "EditSiteDialog",
  components: {
    MapPointDialog
  },

  props: {
    visible: {
      type: Boolean,
      default: false
    },
    siteId: {
      type: [String, Number],
      default: null
    },
    resourceId: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      showMapDialog: false,
      loading: false,
      siteData: null,
      VOLTAGE_LEVEL: {},
      GENERATION_MODE: {},
      generationModeOptions: [], // 发电模式选项
      formData: {
        site_name: "",
        site_type: null,
        device_count: "",
        longitude: "",
        latitude: "",
        contact_person: "",
        phone_number: "",
        site_address: "",
        related_room: "",
        voltage_level: "",
        grid_voltage: "",
        total_capacity: "",
        total_storage: "",
        generation_mode: "",
        operation_date: "",
        imageUrl: ""
      },
      formRules: {
        site_name: [
          {
            required: true,
            message: $T("请输入站点名称"),
            trigger: "blur"
          }
        ],
        site_type: [
          {
            required: true,
            message: $T("请选择站点类型"),
            trigger: "blur"
          }
        ],

        contact_person: [
          { required: true, message: $T("请输入联系人"), trigger: "blur" }
        ],
        phone_number: [
          {
            required: true,
            message: $T("请输入联系电话"),
            trigger: "blur"
          },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: $T("请输入正确的手机号码"),
            trigger: "blur"
          }
        ],
        operation_date: [
          {
            required: true,
            message: $T("请选择投运时间"),
            trigger: "change"
          }
        ],
        voltage_level: [
          {
            required: true,
            message: $T("请选择电压等级"),
            trigger: "change"
          }
        ],
        related_room: [
          {
            required: true,
            message: $T("请选择关联站点"),
            trigger: "change"
          }
        ],
        grid_voltage: [
          {
            required: true,
            message: $T("请输入并网电压"),
            trigger: "blur"
          }
        ],
        total_capacity: [
          {
            required: true,
            message: $T("请输入总装机容量"),
            trigger: "blur"
          }
        ],
        total_storage: [
          {
            required: true,
            message: $T("请输入总储电量"),
            trigger: "blur"
          }
        ],
        generation_mode: [
          {
            required: true,
            message: $T("请选择发电模式"),
            trigger: "change"
          }
        ]
      },

      // 站点选择相关
      roomSelectorVisible: false,
      roomListLoading: false,
      roomTypesLoading: false,
      selectedRoomType: null,
      selectedRoomId: null,
      selectedRoomName: "",
      allRooms: {},
      selectedRooms: [],
      // 站点类型映射（根据实际API返回的类型编码）
      roomTypeMap: {
        1: $T("配电室"),
        2: $T("IT机房"),
        3: $T("空调机房"),
        4: $T("空压机房"),
        5: $T("锅炉房"),
        6: $T("管道房"),
        7: $T("蓄电池房"),
        8: $T("储能电站"),
        9: $T("充电站"),
        10: $T("风电场站"),
        11: $T("网络机房"),
        12: $T("光伏电站"),
        15: $T("调光器室"),
        16: $T("发电机室")
      }
    };
  },
  mounted() {
    this.loadEnumOptions();
  },
  computed: {
    // 地图资源是否已加载
    isLoadBaiduMapResource() {
      if (window.BMap) return true;
      return false;
    },
    // 本地可见状态，避免直接修改prop
    localVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        if (!value) {
          this.handleClose();
        }
      }
    },

    // 当前站点类型分组
    currentGroup() {
      return this.siteData && this.siteData.siteType
        ? this.getSiteTypeGroup(this.siteData.siteType)
        : null;
    },

    // 站点类型名称
    siteTypeName() {
      if (!this.siteData || !this.siteData.siteType) return "";
      return getEnumLabel("SITE_TYPE_CODES", this.siteData.siteType) || "";
    },

    // 动态生成站点类型选项（基于API返回的数据和映射）
    availableRoomTypes() {
      if (!this.allRooms || typeof this.allRooms !== "object") {
        return [];
      }

      const types = Object.keys(this.allRooms)
        .filter(key => {
          const rooms = this.allRooms[key];
          return Array.isArray(rooms) && rooms.length > 0;
        })
        .map(key => {
          const rooms = this.allRooms[key];
          const roomCount = rooms.length;

          return {
            value: key,
            label: this.roomTypeMap[key] || `站点类型${key}`,
            count: roomCount,
            disabled: roomCount === 0,
            rooms: rooms
          };
        })
        .sort((a, b) => {
          // 按站点数量降序排列，站点多的类型排在前面
          return b.count - a.count;
        });

      return types;
    },

    // 根据站点类型过滤的站点列表
    filteredRooms() {
      if (!this.selectedRoomType || !this.allRooms[this.selectedRoomType]) {
        return [];
      }

      return this.allRooms[this.selectedRoomType] || [];
    }
  },
  watch: {
    visible: {
      handler(newVal) {
        if (newVal && this.siteId) {
          this.loadSiteData();
        }
      },
      immediate: true
    }
  },
  methods: {
    openMapDialog() {
      this.showMapDialog = true;
    },
    // 地图对话框确认
    handleLocationConfirm(location) {
      this.formData.latitude = location?.lat;
      this.formData.longitude = location?.lng;
      this.showMapDialog = false;
    },
    // 加载站点数据
    async loadSiteData() {
      if (!this.siteId) return;

      try {
        const response = await getSiteById(this.siteId);

        if (response.code === 0) {
          this.siteData = response.data;
          this.initFormData();
        }
      } catch (error) {
        // 获取站点数据失败
        this.handleClose();
      }
    },

    // 初始化表单数据
    initFormData() {
      if (!this.siteData) return;

      // API返回的是驼峰命名，直接使用
      this.formData = {
        site_name: this.siteData.siteName || "",
        site_type: this.siteData.siteType,
        longitude: this.siteData.longitude || "",
        latitude: this.siteData.latitude || "",
        contact_person: this.siteData.contactPerson || "",
        phone_number: this.siteData.phoneNumber || "",
        site_address: this.siteData.siteAddress || "",
        related_room: this.siteData.roomName || "", // 仅用于显示
        // 扩展字段（可能不存在于基础API中）
        voltage_level: this.siteData.voltageLevel || "",
        grid_voltage: this.siteData.gridVoltage || "",
        total_capacity: this.siteData.totalCapacity || "",
        total_storage: this.siteData.totalStorage || "",
        generation_mode: this.siteData.generationMode || "",
        operation_date: this.siteData.operationDate
          ? typeof this.siteData.operationDate === "number"
            ? new Date(this.siteData.operationDate).toISOString().split("T")[0]
            : this.siteData.operationDate
          : "",
        imageUrl: this.siteData.picturePath || ""
      };

      // 初始化站点信息
      this.selectedRoomId = this.siteData.roomId || null;
      this.selectedRoomName = this.siteData.roomName || ""; // 用于输入框显示
    },

    // 保存
    async handleSave() {
      await this.$refs.editForm.validate();

      const updateData = {
        siteName: this.formData.site_name,
        resourceId: this.resourceId ? Number(this.resourceId) : null,
        longitude: this.formData.longitude
          ? Number(this.formData.longitude)
          : null,
        latitude: this.formData.latitude
          ? Number(this.formData.latitude)
          : null,
        contactPerson: this.formData.contact_person,
        phoneNumber: this.formData.phone_number,
        siteAddress: this.formData.site_address,
        roomId: this.selectedRoomId ? Number(this.selectedRoomId) : null,
        // 扩展字段（根据站点类型可能需要）
        voltageLevel: this.formData.voltage_level
          ? Number(this.formData.voltage_level)
          : null,
        gridVoltage: this.formData.grid_voltage
          ? Number(this.formData.grid_voltage)
          : null,
        totalCapacity: this.formData.total_capacity
          ? Number(this.formData.total_capacity)
          : null,
        totalStorage: this.formData.total_storage
          ? Number(this.formData.total_storage)
          : null,
        generationMode: this.formData.generation_mode || null,
        operationDate: this.formData.operation_date
          ? new Date(this.formData.operation_date).getTime()
          : null,
        picturePath: this.formData.imageUrl || null
      };

      const response = await updateSite(this.siteId, updateData);

      if (response.code === 0) {
        this.$message.success($T("更新站点成功"));
        this.$emit("save", response.data);
        this.handleClose();
      }
    },

    // 关闭
    handleClose() {
      this.$emit("close");
      this.resetForm();
    },

    // 重置表单
    resetForm() {
      this.siteData = null;
      this.formData = {
        site_name: "",
        site_type: null,
        longitude: "",
        latitude: "",
        contact_person: "",
        phone_number: "",
        site_address: "",
        related_room: "",
        voltage_level: "",
        grid_voltage: "",
        total_capacity: "",
        total_storage: "",
        generation_mode: "",
        operation_date: "",
        imageUrl: ""
      };
      // 重置站点信息
      this.selectedRoomId = null;
      this.selectedRoomName = "";
      if (this.$refs.editForm) {
        this.$refs.editForm.clearValidate();
      }
    },

    // 打开站点选择器
    openRoomSelector() {
      this.roomSelectorVisible = true;
      this.loadAllRooms();
    },

    // 加载所有站点数据
    async loadAllRooms() {
      // 调用API获取所有站点数据（强制刷新缓存）
      const response = await getAvailableRooms(true);

      if (response && response.code === 0) {
        this.allRooms = response.data || {};
        await this.$nextTick();
      }
    },

    // 站点类型变化处理
    handleRoomTypeChange(newRoomType) {
      // 清空之前选中的站点
      this.selectedRooms = [];
    },

    // 站点选择变化处理
    handleRoomSelectionChange(selection) {
      this.selectedRooms = selection;
    },

    // 确认站点选择
    confirmRoomSelection() {
      if (this.selectedRooms.length === 0) {
        this.$message.warning($T("请选择至少一个站点"));
        return;
      }

      // 目前只支持单选，取第一个选中的站点
      const selectedRoom = this.selectedRooms[0];
      this.selectedRoomId = selectedRoom.id;
      this.selectedRoomName = selectedRoom.name;
      this.formData.related_room = selectedRoom.name;

      this.roomSelectorVisible = false;
    },

    // 根据站点类型编码获取站点类型名称
    getRoomTypeName(roomType) {
      return this.roomTypeMap[roomType] || `站点类型${roomType}`;
    },

    // 处理图片上传
    async handleImageUpload(file) {
      // 验证文件类型
      const isValidType = ["image/png", "image/jpg", "image/jpeg"].includes(
        file.type
      );
      if (!isValidType) {
        this.$message.error($T("只能上传PNG、JPG、JPEG格式的图片"));
        return false;
      }

      // 验证文件大小（限制为2MB）
      const isValidSize = file.size / 1024 / 1024 < 2;
      if (!isValidSize) {
        this.$message.error($T("图片大小不能超过2MB"));
        return false;
      }

      // 显示上传中状态
      const loading = this.$loading({
        text: $T("图片上传中..."),
        target: document.querySelector(".image-uploader")
      });

      // 调用上传接口
      const response = await uploadImage(file);
      loading.close();

      if (response.code === 0) {
        // 保存图片路径，使用API返回的storedFileName字段
        this.formData.imageUrl = response.data.storedFileName;
        this.$message.success($T("图片上传成功"));
      }

      return false; // 阻止默认上传行为
    },

    // 预览图片
    previewImage() {
      if (!this.formData.imageUrl) return;

      // 使用Element UI的图片预览功能
      const images = [this.formData.imageUrl];
      this.$imagePreview(images, 0);
    },

    // 删除图片
    removeImage() {
      this.$confirm($T("确定要删除这张图片吗？"), $T("确认删除"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning"
      }).then(() => {
        this.formData.imageUrl = "";
        this.$message.success($T("图片删除成功"));
      });
    },

    // 获取图片显示URL
    getImageDisplayUrl(imagePath) {
      if (!imagePath) return "";

      // 如果是base64数据URL（本地预览），直接返回
      if (imagePath.startsWith("data:")) {
        return imagePath;
      }

      // 如果是服务器路径，构建下载URL
      return `/vpp/api/v1/resource-manager/base-config/images/download/${imagePath}`;
    },

    // 获取站点类型分组
    getSiteTypeGroup(siteType) {
      // 根据站点类型返回对应的分组
      const storageTypes = [2, 10]; // 储能类型：用户侧储能、分布式独立储能
      const renewableTypes = [8, 9]; // 可再生能源类型：分布式光伏、分布式风电

      if (storageTypes.includes(siteType)) {
        return "STORAGE";
      } else if (renewableTypes.includes(siteType)) {
        return "RENEWABLE";
      } else {
        return "OTHER";
      }
    },

    // 加载枚举选项
    loadEnumOptions() {
      // 加载电压等级选项
      const voltageLevelOptions = getEnumOptions("VOLTAGE_LEVEL");
      this.VOLTAGE_LEVEL = {};
      voltageLevelOptions.forEach(option => {
        this.VOLTAGE_LEVEL[option.label] = option.value;
      });

      // 加载发电模式选项
      this.generationModeOptions = getEnumOptions("POWER_GENERATION_MODE");
      const generationModeOptions = this.generationModeOptions;
      this.GENERATION_MODE = {};
      generationModeOptions.forEach(option => {
        this.GENERATION_MODE[option.label] = option.value;
      });

      // 如果没有从枚举获取到数据，使用默认值
      if (Object.keys(this.VOLTAGE_LEVEL).length === 0) {
        this.VOLTAGE_LEVEL = {
          "0.4kV": 1,
          "10kV": 2,
          "35kV": 3,
          "110kV": 4
        };
      }

      if (Object.keys(this.GENERATION_MODE).length === 0) {
        this.GENERATION_MODE = {
          SELF_USE: 1, // 自发自用
          FULL_GRID: 2, // 全额上网
          SURPLUS_GRID: 3 // 余电上网
        };
      }
    }
  }
};
</script>

<style scoped>
.edit-site-dialog .dialog-content {
  max-height: 70vh;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.loading-text {
  font-size: 14px;
}

.section-title {
  font-size: 16px;
  border-left: 4px solid var(--ZS);
  padding-left: 12px;
}

.no-data-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.no-data-text {
  font-size: 14px;
}

/* 图片上传样式 */
.image-upload-container {
  width: 100%;
}

.image-preview {
  position: relative;
  display: inline-block;
}

.preview-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 6px;
  cursor: pointer;
  border: 1px solid var(--BG3);
}

.image-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.image-uploader {
  width: 120px;
  height: 120px;
}

.upload-placeholder {
  width: 120px;
  height: 120px;
  border: 2px dashed var(--BG3);
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  background-color: var(--BG2);
}

/* 站点选择器样式 */
.room-selector-btn {
  padding: 0 8px;
  color: var(--ZS);
  font-size: 16px;
}

.room-selector-btn:hover {
  color: var(--ZS-hover);
}

.room-selector-dialog {
  z-index: 3000 !important;
}

.room-selector-dialog .el-dialog__wrapper {
  z-index: 3000 !important;
}

.room-selector-dialog .el-dialog__body {
  padding: 20px;
}

/* 站点类型选择下拉框样式 */
.room-type-select-dropdown {
  z-index: 4000 !important;
}

.filter-section {
  margin-bottom: 16px;
}

.filter-label {
  font-size: 14px;
  margin-bottom: 8px;
}

.room-count {
  color: var(--T3);
  font-size: 12px;
  margin-left: 8px;
}

.no-data-tip {
  padding: 12px 20px;
  text-align: center;
  color: var(--T3);
  font-size: 14px;
}

.no-data-tip i {
  margin-right: 4px;
}

.select-tip {
  padding: 8px 12px;
  color: var(--T3);
  font-size: 12px;
  border-bottom: 1px solid var(--BG3);
  margin-bottom: 4px;
  background-color: var(--BG1);
}

.select-tip i {
  margin-right: 4px;
  color: var(--ZS);
}

.room-list {
  border: 1px solid var(--BG3);
  border-radius: 4px;
}

.upload-placeholder:hover {
  border-color: var(--ZS);
  background-color: var(--BG3);
}

.upload-placeholder i {
  font-size: 32px;
  color: var(--T3);
  margin-bottom: 8px;
}

.upload-text {
  font-size: 12px;
  color: var(--T3);
}
</style>
