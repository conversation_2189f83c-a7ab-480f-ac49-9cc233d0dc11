<template>
  <ElDrawer
    :title="$T('用户详情')"
    :visible.sync="openDrawer"
    destroy-on-close
    size="960px"
  >
    <div class="dialog-content">
      <!-- 用户详情 -->

      <el-row :gutter="16">
        <el-col :span="8" class="mb-J3">
          <div class="detail-item">
            <div class="detail-label text-T3 mb-J1">{{ $T("用户名称") }}</div>
            <div class="detail-value text-T2">
              {{ userDetail.name || "--" }}
            </div>
          </div>
        </el-col>
        <el-col :span="8" class="mb-J3">
          <div class="detail-item">
            <div class="detail-label text-T3 mb-J1">{{ $T("联系人") }}</div>
            <div class="detail-value text-T2">
              {{ userDetail.contact || "--" }}
            </div>
          </div>
        </el-col>
        <el-col :span="8" class="mb-J3">
          <div class="detail-item">
            <div class="detail-label text-T3 mb-J1">{{ $T("联系电话") }}</div>
            <div class="detail-value text-T2">
              {{ userDetail.phone || "--" }}
            </div>
          </div>
        </el-col>
        <el-col :span="8" class="mb-J3">
          <div class="detail-item">
            <div class="detail-label text-T3 mb-J1">{{ $T("资源数量") }}</div>
            <div class="detail-value text-T2">
              {{ userDetail.count || 0 }}
            </div>
          </div>
        </el-col>
        <el-col :span="8" class="mb-J3">
          <div class="detail-item">
            <div class="detail-label text-T3 mb-J1">{{ $T("区域") }}</div>
            <div class="detail-value text-T2">
              {{ userDetail.area || "--" }}
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 地址单独一行 -->
      <el-row :gutter="16">
        <el-col :span="24" class="mb-J3">
          <div class="detail-item">
            <div class="detail-label text-T3 mb-J1">{{ $T("地址") }}</div>
            <div class="detail-value text-T2">
              {{ userDetail.address || "--" }}
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </ElDrawer>
</template>

<script>
export default {
  name: "UserDetailDrawer",
  props: {
    visibleTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    inputData_in: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      openDrawer: false,
      userDetail: {}
    };
  },
  watch: {
    visibleTrigger_in() {
      this.openDrawer = true;
      this.loadUserDetail();
    },
    closeTrigger_in() {
      this.openDrawer = false;
    }
  },
  methods: {
    loadUserDetail() {
      // 直接使用传入的用户详情数据
      this.userDetail = {
        ...this.inputData_in
      };
    }
  }
};
</script>

<style scoped>
.dialog-content {
  height: 100%;
}

.detail-section {
  border-bottom: 1px solid var(--B2);
  padding-bottom: var(--J3);
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  position: relative;
  padding-left: var(--J2);
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: var(--ZS);
  border-radius: 2px;
}

.detail-item {
  min-height: 48px;
}

.detail-label {
  font-size: var(--Ab);
  line-height: 1.4;
}

.detail-value {
  font-size: var(--Aa);
  line-height: 1.4;
  word-break: break-all;
}
</style>
