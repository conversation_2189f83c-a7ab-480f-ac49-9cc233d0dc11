<template>
  <div class="site-management">
    <!-- 操作区 -->
    <div class="filter-bar">
      <el-input
        class="vpp-search-input"
        v-model="searchKeyword"
        :placeholder="$T('请输入关键字')"
        prefix-icon="el-icon-search"
        size="small"
        clearable
        @input="handleSearch"
      />
      <div class="vpp-select-group">
        <div class="vpp-select-item">
          <custom-el-select
            v-model="selectedSiteType"
            placeholder="全部"
            size="small"
            class="w-25"
            clearable
            :prefix_in="$T('站点类型')"
          >
            <el-option
              v-for="item in siteTypeOptions"
              :key="item.id"
              :label="item.text"
              :value="item.id"
            />
          </custom-el-select>
        </div>
      </div>
      <div class="vpp-action-buttons">
        <el-button
          type="danger"
          size="small"
          :disabled="selectedSites.length === 0"
          @click="handleBatchDelete"
          plain
        >
          {{ $T("批量删除") }}
        </el-button>
        <el-button type="primary" size="small" @click="handleAddSite">
          {{ $T("新增站点") }}
        </el-button>
      </div>
    </div>

    <el-table
      class="flex1"
      :data="currentPageData"
      height="true"
      highlight-current-row
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column
        type="index"
        :label="$T('序号')"
        width="80"
        :index="indexMethod"
      />
      <el-table-column
        prop="siteName"
        :label="$T('站点名称')"
        min-width="150"
        show-overflow-tooltip
      />
      <el-table-column
        :label="$T('站点类型')"
        width="120"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ getSiteTypeName(scope.row.siteType) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="deviceCount"
        :label="$T('设备数量（个）')"
        width="140"
      >
        <template slot-scope="scope">
          <span class="text-ZS font-medium">
            {{ scope.row.deviceCount }}
          </span>
        </template>
      </el-table-column>
      <el-table-column :label="$T('操作')" width="180">
        <template slot-scope="scope">
          <span
            class="action-link detail-link"
            @click="handleDetail(scope.row)"
          >
            {{ $T("详情") }}
          </span>
          <span class="action-link edit-link" @click="handleEdit(scope.row)">
            {{ $T("编辑") }}
          </span>
          <span
            class="action-link delete-link"
            @click="handleDelete(scope.row)"
          >
            {{ $T("删除") }}
          </span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页区域 -->
    <div class="table-footer">
      <el-pagination
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :total="totalCount"
        :page-sizes="[10, 20, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>

    <!-- 新增站点弹窗 -->
    <AddSiteDialog
      :visible="addSiteDialogVisible"
      :resourceId="processedResourceId"
      :resourceInfo="currentResourceInfo"
      :vppId="vppId"
      @close="handleAddSiteDialogClose"
      @save="handleAddSiteDialogSave"
    />

    <!-- 站点详情面板 -->
    <SiteDetailPanel
      :visible="showDetailPanel"
      :site-data="currentSiteDetail"
      @close="showDetailPanel = false"
    />

    <!-- 编辑站点对话框 -->
    <EditSiteDialog
      :visible="editSiteDialogVisible"
      :site-id="currentEditSiteId"
      :resource-id="processedResourceId"
      :resourceInfo="currentResourceInfo"
      @close="handleEditSiteDialogClose"
      @save="handleEditSiteDialogSave"
    />
  </div>
</template>
<script>
import AddSiteDialog from "./AddSiteDialog.vue";
import SiteDetailPanel from "./SiteDetailPanel.vue";
import EditSiteDialog from "./EditSiteDialog.vue";
import {
  getSitePage,
  getSiteById,
  deleteSitesByIds
} from "@/api/site-management";

export default {
  name: "SiteManagement",
  components: {
    AddSiteDialog,
    SiteDetailPanel,
    EditSiteDialog
  },
  props: {
    node: {
      type: Object,
      default: () => null
    },
    resourceId: {
      type: Number,
      default: null
    },
    vppId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      // 选中的站点列表
      selectedSites: [],
      // 搜索关键字
      searchKeyword: "",
      // 选中的站点类型
      selectedSiteType: null,
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      total: 0,
      // 站点数据
      allSites: [],
      // 加载状态
      loading: false,
      searchTimer: null,

      // 新增站点弹窗相关
      addSiteDialogVisible: false,
      currentResourceId: null,
      currentResourceInfo: null,

      // 站点详情面板相关
      showDetailPanel: false,
      currentSiteDetail: null,

      // 编辑站点对话框相关
      editSiteDialogVisible: false,
      currentEditSiteId: null
    };
  },
  mounted() {
    // 数据加载由resourceId的watch处理（immediate: true），避免重复调用
  },

  watch: {
    searchKeyword: {
      handler() {
        this.debounceSearch();
      }
    },
    selectedSiteType: {
      handler() {
        this.loadSites();
      }
    },
    resourceId: {
      handler() {
        if (this.resourceId) {
          this.loadSites();
        }
      },
      immediate: true
    }
  },
  computed: {
    // 当前选中的节点（来自父组件props）
    selectedNode() {
      return this.node;
    },

    // 处理后的resourceId - 从tree_id格式转换为数字格式
    processedResourceId() {
      if (!this.resourceId) return null;

      // 如果是tree_id格式（如"resource_1"），提取数字部分
      if (
        typeof this.resourceId === "string" &&
        this.resourceId.startsWith("resource_")
      ) {
        const extracted = Number(this.resourceId.replace("resource_", ""));
        return extracted;
      }

      // 如果已经是数字格式，直接返回
      return Number(this.resourceId);
    },
    // 根据选中节点过滤站点
    filteredSites() {
      let filtered = this.allSites;

      // 根据选中的树节点过滤
      if (this.selectedNode) {
        const nodeType = this.selectedNode.type;
        const nodeId = this.selectedNode.tree_id;
        const originalId = this.selectedNode.originalId; // 使用原始数字ID

        switch (nodeType) {
          case "user": {
            // 用户节点：按用户ID过滤
            const userId =
              originalId ||
              (typeof nodeId === "string" && nodeId.startsWith("user_")
                ? Number(nodeId.replace("user_", ""))
                : nodeId);
            filtered = filtered.filter(item => item.userId === userId);
            break;
          }
          case "resource":
            // 资源节点：API已经根据resourceId参数过滤了数据，无需前端再过滤
            // 不进行过滤，直接使用API返回的数据
            break;
          case "site": {
            // 站点节点：按站点ID过滤
            const siteId =
              originalId ||
              (typeof nodeId === "string" && nodeId.startsWith("site_")
                ? Number(nodeId.replace("site_", ""))
                : nodeId);
            filtered = filtered.filter(item => item.id === siteId);
            break;
          }
          case "vpp":
          default:
            // 显示所有数据
            break;
        }
      }

      // 根据搜索关键字过滤
      if (this.searchKeyword) {
        filtered = filtered.filter(
          item =>
            item.siteName
              .toLowerCase()
              .includes(this.searchKeyword.toLowerCase()) ||
            this.getSiteTypeName(item.siteType)
              .toLowerCase()
              .includes(this.searchKeyword.toLowerCase())
        );
      }

      // 根据站点类型过滤
      if (this.selectedSiteType) {
        filtered = filtered.filter(
          item => item.siteType === this.selectedSiteType
        );
      }

      return filtered;
    },
    // 总数 - 使用API返回的total（API已经处理了resourceId过滤）
    totalCount() {
      return this.total;
    },
    // 总页数
    totalPages() {
      return Math.ceil(this.totalCount / this.pageSize);
    },
    // 当前页的数据 - 使用服务端分页，直接返回API数据
    currentPageData() {
      // API已经处理了所有过滤和分页，直接返回数据
      // 只需要应用前端的搜索和站点类型过滤
      return this.filteredSites;
    },
    // 站点类型选项
    siteTypeOptions() {
      return this.$store.state.enumerations["vpp_site_type"] || [];
    }
  },
  methods: {
    // 防抖搜索
    debounceSearch() {
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.currentPage = 1;
        this.loadSites();
      }, 500);
    },

    // 数据转换：API返回的站点数据已经是驼峰命名，直接使用
    transformSiteData(apiSite) {
      return {
        id: apiSite.id,
        siteName: apiSite.siteName,
        siteType: apiSite.siteType,
        deviceCount: apiSite.deviceCount || 0,
        siteId: apiSite.siteId,
        resourceId: apiSite.resourceId,
        roomId: apiSite.roomId,
        contactPerson: apiSite.contactPerson,
        phoneNumber: apiSite.phoneNumber,
        siteAddress: apiSite.siteAddress,
        longitude: apiSite.longitude,
        latitude: apiSite.latitude,
        createTime: apiSite.createTime,
        updateTime: apiSite.updateTime,
        // 保留原始API数据用于编辑
        originalData: apiSite
      };
    },

    // 数据转换：将组件数据转换为API发送格式(VppSiteDTO-驼峰命名)
    transformToApiFormat(siteData) {
      return {
        id: siteData.id,
        siteName: siteData.site_name,
        siteType: siteData.site_type,
        deviceCount: siteData.device_count,
        siteId: siteData.site_id,
        resourceId: siteData.resource_id,
        roomId: siteData.room_id,
        contactPerson: siteData.contact_person,
        phoneNumber: siteData.phone_number,
        siteAddress: siteData.site_address,
        longitude: siteData.longitude,
        latitude: siteData.latitude
      };
    },

    // 统一的站点详情数据转换：API驼峰命名 → 组件下划线命名
    transformSiteDetailData(apiData) {
      return {
        ...apiData,
        // 基础字段映射（API驼峰 → 组件下划线）
        site_name: apiData.siteName || "",
        site_type: apiData.siteType || null,
        site_address: apiData.siteAddress || "",
        contact_person: apiData.contactPerson || "",
        phone_number: apiData.phoneNumber || "",
        device_count: apiData.deviceCount || 0,
        // 扩展字段映射（这些字段可能在API中不存在，提供默认值）
        operation_date: apiData.operationDate || null,
        related_room: apiData.roomName || null,
        generation_mode: apiData.generationMode || null,
        voltage_level: apiData.voltageLevel || null,
        grid_voltage: apiData.gridVoltage || null,
        total_capacity: apiData.totalCapacity || null,
        total_storage: apiData.totalStorage || null,
        picturePath: apiData.picturePath || null
      };
    },

    // 加载站点列表
    async loadSites() {
      // 统一使用分页查询
      const queryData = {
        pageNum: this.currentPage,
        pageSize: this.pageSize
      };

      // 添加搜索条件 - 注意SiteQueryDTO使用驼峰命名
      if (this.searchKeyword) {
        queryData.siteName = this.searchKeyword;
      }
      if (this.selectedSiteType) {
        queryData.siteType = this.selectedSiteType;
      }
      if (this.vppId) {
        queryData.vppId = this.vppId;
      }
      // 如果有resourceId，添加资源过滤条件
      if (this.processedResourceId) {
        queryData.resourceId = this.processedResourceId;
      }

      const response = await getSitePage(queryData);

      if (response.code === 0 && response.data) {
        this.allSites = response.data.map(site => this.transformSiteData(site));
        this.total = response?.total || 0;
      }
    },

    // 获取站点类型名称
    getSiteTypeName(siteType) {
      const option = this.siteTypeOptions.find(opt => opt.id == siteType);
      return option ? option.text : "--";
    },

    // 序号计算方法
    indexMethod(index) {
      return (this.currentPage - 1) * this.pageSize + index + 1;
    },

    // 处理搜索
    handleSearch() {
      this.currentPage = 1; // 重置到第一页
    },

    // 处理站点类型变化
    handleSiteTypeChange() {
      this.currentPage = 1; // 重置到第一页
    },

    // 处理分页大小变化
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
    },

    // 处理当前页变化
    handleCurrentChange(val) {
      this.currentPage = val;
    },

    // 新增站点
    handleAddSite() {
      // 获取当前选中的资源信息
      if (this.selectedNode && this.selectedNode.type === "resource") {
        this.currentResourceId = this.selectedNode.tree_id;
        this.currentResourceInfo = {
          id: this.selectedNode.tree_id,
          name: this.selectedNode.name,
          type: this.selectedNode.resourceType // 资源类型编码 (1-发电, 2-储电, 3-用电, 4-微电网)
        };

        // 显示新增站点弹窗
        this.addSiteDialogVisible = true;
      } else {
        this.$message.warning($T("请先选择一个资源节点"));
        return;
      }
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedSites = selection;
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedSites.length === 0) {
        this.$message.warning($T("请选择要删除的站点"));
        return;
      }

      // 调用统一的删除方法，传入选中的站点数组
      this.handleDelete(this.selectedSites);
    },

    // 详情
    async handleDetail(item) {
      try {
        // 显示加载状态
        this.showDetailPanel = true;
        this.currentSiteDetail = null;

        // 调用API获取站点详情数据
        const response = await getSiteById(item.id);

        if (response.code === 0) {
          // 使用统一的数据转换方法
          this.currentSiteDetail = this.transformSiteDetailData(response.data);
        } else {
          this.showDetailPanel = false;
        }
      } catch (error) {
        this.showDetailPanel = false;
      }
    },

    // 编辑
    handleEdit(item) {
      this.currentEditSiteId = item.id;
      this.editSiteDialogVisible = true;
      this.currentResourceInfo = {
        id: this.selectedNode.tree_id,
        name: this.selectedNode.name,
        type: this.selectedNode.resourceType // 资源类型编码 (1-发电, 2-储电, 3-用电, 4-微电网)
      };
    },

    // 编辑对话框关闭
    handleEditSiteDialogClose() {
      this.editSiteDialogVisible = false;
      this.currentEditSiteId = null;
    },

    // 编辑对话框保存
    handleEditSiteDialogSave(updatedSite) {
      this.$message.success($T("站点更新成功"));
      this.editSiteDialogVisible = false;
      this.currentEditSiteId = null;
      // 刷新站点列表
      this.loadSites();
      // 通知父组件刷新树形结构
      this.$emit("refresh-tree");
    },

    // 删除（支持单个删除和批量删除）
    handleDelete(items) {
      // 判断是单个删除还是批量删除
      const itemsArray = Array.isArray(items) ? items : [items];
      const isBatch = Array.isArray(items);

      if (itemsArray.length === 0) {
        this.$message.warning($T("请选择要删除的站点"));
        return;
      }

      // 构建确认消息
      let confirmMessage;
      if (isBatch) {
        confirmMessage = $T(
          `确定要删除选中的 ${itemsArray.length} 个站点吗？删除后将无法恢复。`
        );
      } else {
        confirmMessage = $T(
          "确定要删除站点「{0}」吗？删除后将无法恢复。",
          itemsArray[0].siteName || itemsArray[0].name
        );
      }

      this.$confirm(confirmMessage, $T("删除确认"), {
        confirmButtonText: $T("确定"),
        cancelButtonText: $T("取消"),
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: async (action, instance, done) => {
          if (action === "confirm") {
            instance.confirmButtonLoading = false;
            instance.confirmButtonText = $T("删除中...");

            // 调用后端接口删除站点
            const siteIds = itemsArray.map(item => item.id);
            const response = await deleteSitesByIds(siteIds);

            if (response.code === 0) {
              // 显示成功消息
              if (isBatch) {
                this.$message.success(
                  $T(`成功删除 ${itemsArray.length} 个站点`)
                );
                // 清空选中状态（如果是批量删除）
                this.selectedSites = [];
              } else {
                this.$message.success($T("删除成功"));
              }

              // 刷新站点列表数据
              this.loadSites();

              // 通知父组件刷新树形结构
              this.$emit("refresh-tree");
            }
          }
          done();
        }
      }).catch(() => {
        this.$message.info($T("已取消删除"));
      });
    },

    // 新增站点弹窗关闭
    handleAddSiteDialogClose() {
      this.addSiteDialogVisible = false;
      this.currentResourceId = null;
      this.currentResourceInfo = null;
    },

    // 新增站点弹窗保存
    handleAddSiteDialogSave() {
      // 刷新站点列表数据
      this.loadSites();

      // 通知父组件刷新树形结构
      this.$emit("refresh-tree");

      this.$message.success($T("站点新增成功"));
    }
  },
  beforeDestroy() {
    // 清理定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
  }
};
</script>
<style scoped>
.site-management {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--BG1);
  border-radius: var(--Ra);
  padding: var(--J4);
  margin-bottom: var(--J3);
}

/* 操作链接样式 */
.action-link {
  font-size: 14px;
  cursor: pointer;
  margin-right: 16px;
  text-decoration: none;
  transition: opacity 0.2s;
}

.detail-link {
  color: var(--ZS);
}

.detail-link:hover {
  opacity: 0.8;
}

.edit-link {
  color: var(--ZS);
}

.edit-link:hover {
  opacity: 0.8;
}

.delete-link {
  color: var(--Sta3);
}

.delete-link:hover {
  opacity: 0.8;
}

/* 筛选栏样式 */
.filter-bar {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--J3);
  margin-bottom: var(--J3);
}

.vpp-search-input {
  width: 240px;
  height: 32px;
}

.vpp-search-input .el-input__inner {
  height: 32px;
  line-height: 32px;
  padding: 1px 1px 1px var(--J1);
  border: 1px solid var(--B1);
  border-radius: var(--Ra);
  background: var(--BG1);
  font-size: var(--Aa);
  color: var(--T1);
  box-shadow: none;
}

.vpp-search-input .el-input__inner::placeholder {
  color: var(--T4);
  font-size: var(--Aa);
}

.vpp-search-input .el-input__inner:focus {
  border-color: var(--ZS);
  box-shadow: none;
}

.vpp-search-input .el-input__prefix {
  left: var(--J1);
}

.vpp-search-input .el-input__prefix .el-input__icon {
  color: var(--T4);
  font-size: var(--J2);
  line-height: 32px;
}

.vpp-select-group {
  display: flex;
  gap: var(--J2);
  flex: 1;
}

.vpp-select-item {
  display: flex;
  align-items: center;
  gap: var(--J1);
  min-width: 120px;
}

.vpp-select-label {
  color: var(--ZS);
  font-size: var(--Ab);
  font-weight: 400;
  line-height: var(--J2);
  white-space: nowrap;
}

.vpp-select-item .el-select .el-input__inner {
  border: 1px solid var(--B1);
  border-radius: var(--Ra);
  background: var(--BG1);
  color: var(--T1);
  font-size: var(--Aa);
  padding: 0 var(--J1);
  height: 32px;
  line-height: 32px;
}

.vpp-select-item .el-select .el-input__inner:focus {
  border-color: var(--ZS);
  box-shadow: none;
}

.vpp-select-item .el-select .el-input__suffix {
  right: 0;
}

.vpp-select-item .el-select .el-input__suffix .el-input__icon {
  color: var(--T3);
  font-size: var(--Ab);
}

/* 按钮组样式 */
.vpp-action-buttons {
  display: flex;
  align-items: center;
  /* gap: var(--J1); */
}

/* 表格样式 */
.site-management .el-table {
  background: var(--BG1);
  border-radius: var(--Ra);
}

.site-management .el-table th {
  background: var(--BG1);
  border-bottom: 1px solid var(--B2);
  color: var(--T3);
  font-size: var(--Ab);
  font-weight: 400;
  line-height: var(--J2);
  padding: var(--J1) var(--J3);
}

.site-management .el-table td {
  border-bottom: 1px solid var(--B2);
  color: var(--T1);
  font-size: var(--Aa);
  font-weight: 400;
  line-height: var(--J3);
  padding: var(--J1) var(--J3);
}

/* 分页器样式 */
.table-footer {
  display: flex;
  align-items: center;
  justify-content: right;
  gap: var(--J2);
  margin-top: var(--J2);
  font-size: var(--Aa);
  color: var(--T1);
}

.total-highlight {
  color: var(--ZS);
  font-weight: 500;
}

.table-footer .el-pagination {
  display: flex;
  align-items: center;
  gap: var(--J1);
}

.table-footer .el-pagination .el-pager li {
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  border-radius: var(--Ra);
  border: 1px solid var(--B2);
  background: var(--BG1);
  color: var(--T1);
  font-size: var(--Aa);
  margin: 0;
}

.table-footer .el-pagination .el-pager li.active {
  background: var(--BG2);
  color: var(--T1);
  border-color: var(--B2);
}

.table-footer .el-pagination .btn-prev,
.table-footer .el-pagination .btn-next {
  width: 32px;
  height: 32px;
  line-height: 32px;
  border-radius: var(--Ra);
  border: 1px solid var(--B2);
  background: var(--BG1);
  color: var(--T1);
}

.table-footer .el-pagination .el-pagination__jump {
  margin-left: var(--J2);
  color: var(--T1);
  font-size: var(--Aa);
}

.table-footer .el-pagination .el-pagination__jump .el-input__inner {
  width: 48px;
  height: 32px;
  border: 1px solid var(--B2);
  border-radius: var(--Ra);
  text-align: center;
}

/* flex1 样式 - 让表格占用剩余高度 */
.flex1 {
  flex: 1;
  min-width: 0;
  min-height: 0;
}

/* 响应式设计 */
@media (max-width: 900px) {
  .filter-bar {
    flex-direction: column;
    gap: var(--J1);
    align-items: stretch;
  }

  .vpp-search-input {
    width: 100%;
  }

  .vpp-select-group {
    flex-direction: column;
    gap: var(--J1);
  }

  .vpp-select-item {
    min-width: auto;
    width: 100%;
  }

  .vpp-action-buttons {
    gap: var(--J1);
    flex-wrap: wrap;
  }

  .table-footer {
    flex-direction: column;
    gap: var(--J1);
    align-items: center;
  }
}
</style>
