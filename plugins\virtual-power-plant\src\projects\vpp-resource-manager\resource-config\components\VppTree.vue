<template>
  <!-- 树形组件容器 -->
  <div class="tree-container">
    <CetGiantTree
      v-bind="CetGiantTree_config"
      v-on="CetGiantTree_config.event"
      class="vpp-giant-tree"
    />
  </div>
</template>

<script>
import {
  getAllVppTrees,
  transformTreeData,
  flattenTreeData
} from "@/api/vpp-tree-cache";

export default {
  name: "VppTree",
  props: {
    selectNode: {
      type: Object
    }
  },
  data() {
    return {
      CetGiantTree_config: {
        inputData_in: [],
        checkedNodes: [],
        selectNode: {},
        setting: {
          check: {
            enable: false // 单选模式
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id",
              pIdKey: "pId",
              rootPId: null
            }
          },
          view: {
            showIcon: true, // 显示图标
            showLine: false // 不显示连接线
          }
        },
        event: {
          created_out: this.onTreeCreated,
          currentNode_out: this.onNodeClick
        }
      }
    };
  },
  watch: {},
  mounted() {
    this.loadTreeData();
  },
  methods: {
    /**
     * 从API加载树形数据
     */
    async loadTreeData() {
      let response;
      // 加载所有VPP树
      response = await getAllVppTrees();

      if (response && response.code === 0 && response.data) {
        // 转换API数据格式为组件需要的格式
        const transformedData = transformTreeData(response.data);

        // 扁平化为zTree格式
        const flattenedData = flattenTreeData(transformedData);

        this.CetGiantTree_config.inputData_in = flattenedData;

        // 延迟触发tree-data-updated事件，确保isLoadingFromApi标志已重置
        this.$emit("tree-data-updated", flattenedData);
        // 选中第一个节点
        this.selectFirstNode(flattenedData);
      } else {
        // 加载树形数据失败
        this.CetGiantTree_config.inputData_in = [];
      }
    },

    /**
     * 刷新树形数据
     */
    async refreshTreeData() {
      // 直接重新获取数据，不调用refreshVppTreeCache
      await this.loadTreeData();
    },

    /**
     * 选中第一个节点的通用方法
     */
    selectFirstNode(data) {
      if (!_.isEmpty(this.selectNode)) {
        this.CetGiantTree_config.selectNode = this.selectNode;
      } else {
        this.CetGiantTree_config.selectNode = data?.[0];
      }
    },

    /**
     * 树创建完成事件
     */
    onTreeCreated(treeObj) {
      this.treeObj = treeObj;
    },

    /**
     * 节点点击事件
     */
    onNodeClick(node) {
      this.$emit("node-click", node);
    }
  }
};
</script>

<style lang="scss" scoped>
.tree-container {
  height: 100%;

  .vpp-giant-tree {
    height: 100%;
  }

  // 调整树节点文字与图标的间距

  // 调整展开/收起箭头的间距

  // VPP节点不显示图标
  :deep(.gianttree .ztree li span.button.vpp-icon_ico_open),
  :deep(.gianttree .ztree li span.button.vpp-icon_ico_close),
  :deep(.gianttree .ztree li span.button.vpp-icon_ico_docu) {
    display: none !important;
  }

  // 用户节点图标 - 灰色调
  :deep(.gianttree .ztree li span.button.user-icon_ico_open),
  :deep(.gianttree .ztree li span.button.user-icon_ico_close),
  :deep(.gianttree .ztree li span.button.user-icon_ico_docu) {
    background: url("~@/assets/images/user-icon.svg") no-repeat center !important;
    // background-size: 16px 16px !important;
    filter: brightness(0) saturate(100%) invert(70%) sepia(0%) saturate(0%)
      hue-rotate(0deg) brightness(95%) contrast(90%);
  }

  // 资源节点图标 - 灰色调
  :deep(.gianttree .ztree li span.button.resource-icon_ico_open),
  :deep(.gianttree .ztree li span.button.resource-icon_ico_close),
  :deep(.gianttree .ztree li span.button.resource-icon_ico_docu) {
    background: url("~@/assets/images/resource-icon.svg") no-repeat center !important;
    // background-size: 16px 16px !important;
    filter: brightness(0) saturate(100%) invert(70%) sepia(0%) saturate(0%)
      hue-rotate(0deg) brightness(95%) contrast(90%);
  }

  // 站点节点图标 - 灰色调
  :deep(.gianttree .ztree li span.button.site-icon_ico_open),
  :deep(.gianttree .ztree li span.button.site-icon_ico_close),
  :deep(.gianttree .ztree li span.button.site-icon_ico_docu) {
    background: url("~@/assets/images/site-icon.svg") no-repeat center !important;
    // background-size: 16px 16px !important;
    filter: brightness(0) saturate(100%) invert(70%) sepia(0%) saturate(0%)
      hue-rotate(0deg) brightness(95%) contrast(90%);
  }

  // 设备节点图标 - 灰色调
  :deep(.gianttree .ztree li span.button.device-icon_ico_open),
  :deep(.gianttree .ztree li span.button.device-icon_ico_close),
  :deep(.gianttree .ztree li span.button.device-icon_ico_docu) {
    background: url("~@/assets/images/device-icon.svg") no-repeat center !important;
    // background-size: 16px 16px !important;
    filter: brightness(0) saturate(100%) invert(70%) sepia(0%) saturate(0%)
      hue-rotate(0deg) brightness(95%) contrast(90%);
  }
}
</style>
