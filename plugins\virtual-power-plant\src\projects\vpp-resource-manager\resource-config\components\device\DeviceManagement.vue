<template>
  <div class="device-management-container">
    <div class="device-management">
      <!-- 设备表格 -->
      <el-table
        class="flex1"
        :data="filteredDevices"
        height="true"
        highlight-current-row
        style="width: 100%"
        v-loading="tableLoading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column :label="$T('序号')" width="80" align="center">
          <template slot-scope="scope">
            {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="deviceName"
          :label="$T('设备名称')"
          min-width="200"
        />
        <el-table-column
          prop="deviceType"
          :label="$T('设备类型')"
          width="150"
        />
        <el-table-column
          prop="ratedPower"
          :label="$T('额定功率（kW）')"
          width="160"
        />
        <el-table-column :label="$T('操作')" width="200">
          <template slot-scope="scope">
            <span
              class="action-link detail-link"
              @click="handleDetail(scope.row)"
            >
              {{ $T("详情") }}
            </span>
            <span class="action-link edit-link" @click="handleEdit(scope.row)">
              {{ $T("编辑") }}
            </span>
            <span
              class="action-link delete-link"
              @click="handleDelete(scope.row)"
            >
              {{ $T("删除") }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 新增/编辑设备弹窗 -->
    <el-dialog
      :title="isEditMode ? $T('编辑设备') : $T('新增设备')"
      :visible.sync="addDeviceDialogVisible"
      width="640px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleDialogClose"
    >
      <div class="dialog-content">
        <el-form
          ref="addDeviceForm"
          :model="addDeviceFormData"
          :rules="addDeviceFormRules"
          label-position="top"
        >
          <el-row :gutter="30">
            <el-col :span="12">
              <!-- 设备名称 -->
              <el-form-item :label="$T('设备名称')" prop="deviceName" required>
                <el-input
                  v-model="addDeviceFormData.deviceName"
                  :placeholder="$T('请输入内容')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <!-- 设备类型 -->
              <el-form-item :label="$T('设备类型')" prop="deviceType" required>
                <el-select
                  v-model="addDeviceFormData.deviceType"
                  :placeholder="$T('请选择')"
                  style="width: 100%"
                  :disabled="isEditMode"
                  @change="handleDeviceTypeChange"
                >
                  <el-option
                    v-for="item in deviceTypeOptions"
                    :key="item.id"
                    :label="item.text"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="30">
            <el-col :span="12">
              <!-- 管网设备 -->
              <el-form-item
                :label="$T('管网设备')"
                prop="networkDevice"
                required
              >
                <div class="network-device-input">
                  <el-input
                    v-model="addDeviceFormData.networkDevice"
                    :placeholder="$T('请选择')"
                    readonly
                  />
                  <i
                    class="el-icon-edit network-device-icon"
                    @click="selectNetworkDevice"
                  ></i>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer">
        <el-button plain @click="handleCancel">{{ $T("取消") }}</el-button>
        <el-button type="success" @click="handleConfirm">
          {{ $T("确定") }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 管网设备选择弹窗 -->
    <el-dialog
      :title="$T('选择管网设备')"
      :visible.sync="networkDeviceDialogVisible"
      width="800px"
      :append-to-body="true"
      :close-on-click-modal="false"
    >
      <div class="dialog-content">
        <!-- 搜索栏 -->
        <div class="search-bar">
          <el-input
            v-model="networkDeviceSearchKeyword"
            :placeholder="$T('请输入设备名称')"
            class="search-input"
            clearable
            @input="handleNetworkDeviceSearch"
          >
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>
        </div>

        <!-- 管网设备列表 -->
        <el-table
          :data="networkDevices"
          :loading="networkDeviceLoading"
          style="width: 100%; margin-top: 16px"
          height="400"
          ref="networkDevicesTable"
          row-key="treeId"
          @selection-change="handleNetworkDeviceSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column
            prop="name"
            :label="$T('设备名称')"
            min-width="150"
          />
          <el-table-column :label="$T('设备类型')" width="120">
            <template slot-scope="scope">
              {{ getNetworkDeviceTypeName(scope.row.modellabel) }}
            </template>
          </el-table-column>
          <el-table-column prop="deviceId" :label="$T('设备ID')" width="120" />
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container" v-if="networkDeviceTotal > 10">
          <el-pagination
            @size-change="handleNetworkDeviceSizeChange"
            @current-change="handleNetworkDeviceCurrentChange"
            :current-page="networkDeviceCurrentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="networkDevicePageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="networkDeviceTotal"
          />
        </div>
      </div>

      <div slot="footer">
        <el-button @click="networkDeviceDialogVisible = false">
          {{ $T("取消") }}
        </el-button>
        <el-button
          type="primary"
          @click="confirmNetworkDeviceSelection"
          :disabled="selectedNetworkDevices.length === 0"
        >
          {{ $T("确定") }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 设备详情抽屉 -->
    <DeviceDetailDrawer
      :visible.sync="deviceDetailDrawerVisible"
      :device-detail="currentDeviceDetail"
      @close="handleDetailDrawerClose"
    />
  </div>
</template>
<script>
// 导入API和组件
import {
  getDevicePage,
  createDevice,
  getDeviceById,
  updateDevice,
  deleteDevice,
  batchDeleteDevices,
  checkDeviceIdExists
} from "@/api/device-management";
import {
  getMonitorDevicesByRoom,
  getDeviceMonitorRelations
} from "@/api/base-config";
import { getSiteById } from "@/api/site-management";
import { processDeviceData } from "@/utils/deviceTypeUtils";
import DeviceDetailDrawer from "./DeviceDetailDrawer";

export default {
  name: "DeviceManagement",
  components: {
    DeviceDetailDrawer
  },
  props: {
    siteId: {
      type: Number,
      default: null
    },
    vppId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      searchKeyword: "",
      selectedCategory: "",
      currentPage: 1,
      pageSize: 10,
      total: 0,
      selectedDevices: [], // 选中的设备列表
      devices: [], // 设备列表数据
      loading: false,
      tableLoading: false,
      searchTimer: null,

      // 新增设备弹窗相关
      addDeviceDialogVisible: false, // 弹窗显示状态
      isEditMode: false, // 是否为编辑模式
      currentEditDeviceId: null, // 当前编辑的设备ID

      // 设备详情抽屉相关
      deviceDetailDrawerVisible: false, // 抽屉显示状态
      currentDeviceDetail: null, // 当前查看的设备详情

      // 管网设备选择相关
      networkDeviceDialogVisible: false,
      networkDevices: [],
      networkDeviceLoading: false,
      networkDeviceSearchKeyword: "",
      networkDeviceCurrentPage: 1,
      networkDevicePageSize: 10,
      networkDeviceTotal: 0,
      selectedNetworkDevices: [],
      networkDeviceSearchTimer: null, // 管网设备搜索防抖定时器
      siteInfo: null, // 站点信息，包含房间信息
      deviceMonitorRelations: [], // 电厂设备与管网设备关联关系
      allowedNetworkDeviceTypes: [], // 当前设备类型允许关联的管网设备类型
      // 新增设备表单数据
      addDeviceFormData: {
        deviceName: "",
        deviceType: "",
        networkDevice: "",
        networkDeviceId: "", // 管网设备ID
        networkDeviceModelLabel: "" // 管网设备类型标识
      },

      // 新增设备表单验证规则
      addDeviceFormRules: {
        deviceName: [
          {
            required: true,
            message: $T("请输入设备名称"),
            trigger: "blur"
          },
          {
            max: 50,
            message: $T("设备名称不能超过50个字符"),
            trigger: "blur"
          }
        ],
        deviceType: [
          {
            required: true,
            message: $T("请选择设备类型"),
            trigger: "change"
          }
        ],
        networkDevice: [
          {
            required: true,
            message: $T("请选择管网设备"),
            trigger: "change"
          }
        ]
      }
    };
  },
  mounted() {
    // 数据加载由siteId的watch处理（immediate: true），避免重复调用
  },
  watch: {
    searchKeyword: {
      handler() {
        this.debounceSearch();
      }
    },
    selectedCategory: {
      handler() {
        this.currentPage = 1;
        this.loadDevices();
      }
    },
    siteId: {
      handler() {
        if (this.siteId) {
          this.loadDevices();
        }
      },
      immediate: true
    }
  },
  computed: {
    filteredDevices() {
      // 如果是按站点加载，使用客户端过滤
      if (this.siteId) {
        let filtered = this.devices;

        // 按设备名称搜索
        if (this.searchKeyword) {
          filtered = filtered.filter(device =>
            device.deviceName
              .toLowerCase()
              .includes(this.searchKeyword.toLowerCase())
          );
        }

        // 按设备类型筛选
        if (this.selectedCategory) {
          filtered = filtered.filter(
            device => device.deviceType === this.selectedCategory
          );
        }

        // 分页
        const start = (this.currentPage - 1) * this.pageSize;
        const end = start + this.pageSize;
        return filtered.slice(start, end);
      } else {
        // 服务端分页，直接返回设备列表
        return this.devices;
      }
    },
    totalDevices() {
      return this.siteId ? this.devices.length : this.total;
    },
    // 设备类型枚举
    deviceTypeOptions() {
      return this.$store.state.enumerations["vpp_device_type"] || [];
    }
  },
  methods: {
    // 防抖搜索
    debounceSearch() {
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.currentPage = 1;
        this.loadDevices();
      }, 500);
    },

    // 数据转换：将API返回的设备数据转换为组件显示格式
    transformDeviceData(apiDevice) {
      return {
        id: apiDevice.id,
        deviceName: apiDevice.deviceName,
        deviceType: this.getDeviceTypeNameById(apiDevice.deviceType), // 转换为中文名称显示
        deviceSubtype: apiDevice.deviceSubType,
        deviceStatus: apiDevice.deviceStatus,
        ratedPower: apiDevice.ratedPower,
        deviceId: apiDevice.deviceId,
        siteId: apiDevice.siteId,
        manufacturer: apiDevice.manufacturer,
        model: apiDevice.model,
        maxWorkingPower: apiDevice.maxWorkingPower,
        minWorkingPower: apiDevice.minWorkingPower,
        position: apiDevice.position,
        installationDate: apiDevice.installationDate,
        operationDate: apiDevice.operationDate,
        createTime: apiDevice.createTime,
        updateTime: apiDevice.updateTime,
        // 保留原始API数据用于编辑
        originalData: apiDevice
      };
    },

    // 加载设备列表
    async loadDevices() {
      this.tableLoading = false;
      try {
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize
        };

        // 添加搜索条件
        if (this.searchKeyword) {
          params.deviceName = this.searchKeyword;
        }
        if (this.selectedCategory) {
          // selectedCategory现在已经是数字ID了，直接使用
          params.deviceType = this.selectedCategory;
        }
        if (this.siteId) {
          params.siteId = this.siteId;
        }

        const response = await getDevicePage(params);

        if (response.code === 0) {
          this.devices = response.data.records.map(device =>
            this.transformDeviceData(device)
          );
          this.total = response.data.total;
          this.currentPage = response.data.pageNum;
          this.pageSize = response.data.pageSize;
        }
      } finally {
        this.tableLoading = false;
      }
    },

    // 打开新增设备弹窗
    handleAdd() {
      // 重置表单和编辑模式
      this.resetAddDeviceForm();
      this.isEditMode = false;
      this.currentEditDeviceId = null;
      // 打开弹窗
      this.addDeviceDialogVisible = true;
    },

    // 弹窗关闭处理
    handleDialogClose() {
      // 重置表单和编辑模式
      this.resetAddDeviceForm();
      this.isEditMode = false;
      this.currentEditDeviceId = null;
    },

    // 取消按钮处理
    handleCancel() {
      this.addDeviceDialogVisible = false;
      this.isEditMode = false;
      this.currentEditDeviceId = null;
    },

    // 确定按钮处理
    handleConfirm() {
      // 表单验证
      this.$refs.addDeviceForm.validate(valid => {
        if (valid) {
          // 根据设备类型名称获取枚举ID
          const deviceTypeId = this.getDeviceTypeIdByName(
            this.addDeviceFormData.deviceType
          );

          // 构造提交数据
          const deviceData = {
            deviceName: this.addDeviceFormData.deviceName.trim(),
            deviceType: deviceTypeId || this.addDeviceFormData.deviceType, // 使用设备类型枚举ID
            siteId: this.siteId // 添加站点ID
          };

          // 如果选择了管网设备，添加管网设备关联信息
          if (
            this.selectedNetworkDevices &&
            this.selectedNetworkDevices.length > 0
          ) {
            deviceData.monitorDeviceRelations = this.selectedNetworkDevices.map(
              device => ({
                id: device.deviceId || device.id, // 兼容不同的字段名
                name: device.name,
                modelLabel: device.modelLabel || device.modellabel // 兼容不同的字段名
              })
            );
          }

          // 调用API保存设备数据
          this.saveDevice(deviceData);
        } else {
          this.$message.error($T("请检查输入信息"));
        }
      });
    },

    // 保存设备数据
    async saveDevice(deviceData) {
      let res;
      if (this.isEditMode) {
        // 编辑模式：调用更新API
        res = await updateDevice(this.currentEditDeviceId, deviceData);
      } else {
        // 新增模式：调用创建API
        res = await createDevice(deviceData);
      }

      if (res.code === 0) {
        this.$message.success(
          this.isEditMode ? $T("编辑设备成功") : $T("新增设备成功")
        );
        this.addDeviceDialogVisible = false;
        this.isEditMode = false;
        this.currentEditDeviceId = null;
        this.loadDevices(); // 重新加载设备列表

        // 通知父组件刷新树形结构
        this.$emit("refresh-tree");
      }
    },

    // 选择管网设备
    async selectNetworkDevice() {
      // 检查是否已选择设备类型
      if (!this.addDeviceFormData.deviceType) {
        this.$message.warning($T("请先选择设备类型"));
        return;
      }

      // 检查是否有允许关联的管网设备类型
      if (!this.allowedNetworkDeviceTypes.length) {
        this.$message.warning($T("当前设备类型无可关联的管网设备类型"));
        return;
      }

      // 首先获取站点信息
      if (!this.siteInfo && this.siteId) {
        await this.loadSiteInfo();
      }

      if (!this.siteInfo || !this.siteInfo.roomId) {
        this.$message.warning($T("当前站点未关联房间，无法查询管网设备"));
        return;
      }
      // 加载管网设备列表
      await this.loadNetworkDevices();
      // 打开管网设备选择弹窗
      this.networkDeviceDialogVisible = true;
    },

    // 重置新增设备表单
    resetAddDeviceForm() {
      this.addDeviceFormData = {
        deviceName: "",
        deviceType: "",
        networkDevice: "",
        networkDeviceId: "",
        networkDeviceModelLabel: ""
      };

      // 清空管网设备选择
      this.selectedNetworkDevices = [];
      this.allowedNetworkDeviceTypes = [];

      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.addDeviceForm) {
          this.$refs.addDeviceForm.clearValidate();
        }
      });
    },

    // 查看设备详情
    async handleDetail(row) {
      // 显示抽屉并清空之前的数据
      this.deviceDetailDrawerVisible = true;
      this.currentDeviceDetail = null;

      // 调用API获取设备详情
      const response = await getDeviceById(row.id);

      if (response.code === 0) {
        // 设置设备详情数据 - DeviceDetailDrawer will handle the network device processing
        this.currentDeviceDetail = response.data;
      } else {
        this.deviceDetailDrawerVisible = false;
      }
    },

    // 详情抽屉关闭处理
    handleDetailDrawerClose() {
      this.currentDeviceDetail = null;
    },

    async handleEdit(row) {
      // 设置编辑模式
      this.isEditMode = true;
      this.currentEditDeviceId = row.id;

      // 调用API获取设备详情
      const response = await getDeviceById(row.id);

      if (response.code === 0) {
        const deviceData = response.data;

        // 预填充表单数据
        this.addDeviceFormData = {
          deviceName: deviceData.deviceName || "",
          deviceType: deviceData.deviceType || "",
          networkDevice: "",
          networkDeviceId: "",
          networkDeviceModelLabel: ""
        };

        // 重置已选择的管网设备
        this.selectedNetworkDevices = [];

        // 处理关联的管网设备信息
        if (
          deviceData.monitor_device_relations &&
          deviceData.monitor_device_relations.length > 0
        ) {
          // 将已关联的设备加入选择列表
          this.selectedNetworkDevices = deviceData.monitor_device_relations.map(
            relation => ({
              deviceId: relation.deviceId,
              name: relation.name,
              modelLabel: relation.modelLabel,
              treeId: relation.treeId
            })
          );

          // 生成显示名称
          const deviceNames = this.selectedNetworkDevices.map(
            device => device.name
          );
          this.addDeviceFormData.networkDevice =
            deviceNames.length > 3
              ? deviceNames.slice(0, 3).join(", ") +
                $T(" 等 {0} 个设备", deviceNames.length - 3)
              : deviceNames.join(", ");
        }

        // 更新允许的管网设备类型
        if (deviceData.deviceType) {
          this.updateAllowedNetworkDeviceTypes(deviceData.deviceType);
        }

        // 显示弹窗
        this.addDeviceDialogVisible = true;
      }
    },

    handleDelete(row) {
      // 弹出删除确认对话框
      this.$confirm(
        $T("确定要删除设备「{0}」吗？删除后将无法恢复。", row.deviceName),
        $T("删除确认"),
        {
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        }
      )
        .then(async () => {
          // 构造删除参数
          const deleteNode = {
            parentId: this.siteId, // 站点ID作为parentId
            ids: [row.id] // 设备ID数组
          };

          const response = await deleteDevice(deleteNode);

          if (response.code === 0) {
            this.$message.success($T("删除设备成功"));

            // 重新加载设备列表
            this.loadDevices();

            // 通知父组件刷新树形结构
            this.$emit("refresh-tree");
          }
        })
        .catch(() => {
          this.$message.info($T("已取消删除"));
        });
    },

    handleSelectionChange(selection) {
      this.selectedDevices = selection;
    },
    handleBatchDelete() {
      if (this.selectedDevices.length === 0) {
        this.$message.warning($T("请选择要删除的设备"));
        return;
      }

      this.$confirm(
        $T("确定要删除选中的 {0} 个设备吗？", this.selectedDevices.length),
        $T("批量删除确认"),
        {
          confirmButtonText: $T("确定"),
          cancelButtonText: $T("取消"),
          type: "warning"
        }
      )
        .then(async () => {
          const deviceIds = this.selectedDevices.map(device => device.id);

          // 构造删除参数
          const deleteNode = {
            parentId: this.siteId, // 站点ID作为parentId
            ids: deviceIds // 设备ID数组
          };

          const response = await batchDeleteDevices(deleteNode);

          if (response.code === 0) {
            this.$message.success(
              $T("成功删除 {0} 个设备", this.selectedDevices.length)
            );

            // 清空选中状态
            this.selectedDevices = [];

            // 重新加载数据
            this.loadDevices();

            // 通知父组件刷新树形结构
            this.$emit("refresh-tree");
          }
        })
        .catch(() => {
          this.$message.info($T("已取消删除"));
        });
    },
    handleSizeChange(newSize) {
      this.pageSize = newSize;
      this.currentPage = 1;
      this.loadDevices();
    },
    handleCurrentChange(newPage) {
      this.currentPage = newPage;
      this.loadDevices();
    },
    getStatusClass(status) {
      switch (status) {
        case "正常运行":
          return "status-normal";
        case "故障":
          return "status-error";
        case "维护中":
          return "status-maintenance";
        default:
          return "";
      }
    },

    // 加载站点信息
    async loadSiteInfo() {
      if (!this.siteId) {
        return;
      }

      const res = await getSiteById(this.siteId);
      if (res.code === 0) {
        this.siteInfo = res.data;
      }
    },

    // 加载管网设备列表
    async loadNetworkDevices() {
      if (!this.siteInfo || !this.siteInfo.roomId) {
        this.networkDevices = [];
        return;
      }

      const queryData = {
        roomId: this.siteInfo.roomId,
        roomType: this.siteInfo.roomType || "",
        siteId: this.siteId, // 添加站点ID
        deviceTypes: this.allowedNetworkDeviceTypes, // 根据设备类型过滤管网设备类型
        vppDeviceId: this.currentEditDeviceId || null // 当前电厂设备ID，用于获取已关联和允许关联的设备
      };

      const res = await getMonitorDevicesByRoom(queryData);

      if (res.code === 0) {
        this.networkDevices = res.data;

        // 使用 $nextTick 确保表格渲染完成后再设置选中状态
        this.$nextTick(() => {
          this.setNetworkDeviceSelection();
        });
      } else {
        this.networkDevices = [];
      }
    },

    // 管网设备搜索
    handleNetworkDeviceSearch() {
      // 防抖处理
      clearTimeout(this.networkDeviceSearchTimer);
      this.networkDeviceSearchTimer = setTimeout(() => {
        this.networkDeviceCurrentPage = 1;
        this.loadNetworkDevices();
      }, 500);
    },

    // 管网设备分页大小改变
    handleNetworkDeviceSizeChange(size) {
      this.networkDevicePageSize = size;
      this.networkDeviceCurrentPage = 1;
      this.loadNetworkDevices();
    },

    // 管网设备当前页改变
    handleNetworkDeviceCurrentChange(page) {
      this.networkDeviceCurrentPage = page;
      this.loadNetworkDevices();
    },

    // 管网设备选择改变
    handleNetworkDeviceSelectionChange(selection) {
      this.selectedNetworkDevices = selection;
    },

    // 设置管网设备选中状态
    setNetworkDeviceSelection() {
      if (
        !this.$refs.networkDevicesTable ||
        !this.selectedNetworkDevices.length
      ) {
        return;
      }

      // 清空当前选中状态
      this.$refs.networkDevicesTable.clearSelection();

      // 根据treeId匹配并设置选中状态
      this.selectedNetworkDevices.forEach(selectedDevice => {
        const matchedDevice = this.networkDevices.find(device => {
          return device.treeId === selectedDevice.treeId;
        });

        if (matchedDevice) {
          this.$refs.networkDevicesTable.toggleRowSelection(
            matchedDevice,
            true
          );
        }
      });
    },

    // 确认管网设备选择
    confirmNetworkDeviceSelection() {
      if (this.selectedNetworkDevices.length === 0) {
        this.$message.warning($T("请选择管网设备"));
        return;
      }

      // 多选模式：使用选中的所有设备
      const selectedDevices = this.selectedNetworkDevices;

      // 生成显示名称（最多显示前3个设备名）
      const deviceNames = selectedDevices.map(device => device.name);
      const displayName =
        deviceNames.length > 3
          ? deviceNames.slice(0, 3).join(", ") +
            $T(" 等 {0} 个设备", deviceNames.length - 3)
          : deviceNames.join(", ");

      this.addDeviceFormData.networkDevice = displayName;

      // 关闭弹窗
      this.networkDeviceDialogVisible = false;
      this.$message.success(
        $T("成功选择 {0} 个管网设备", this.selectedNetworkDevices.length)
      );
    },

    // 加载电厂设备与管网设备关联关系
    async loadDeviceMonitorRelations() {
      const res = await getDeviceMonitorRelations();
      if (res.code === 0) {
        this.deviceMonitorRelations = res.data || [];
      }
    },

    // 设备类型改变处理
    handleDeviceTypeChange(deviceType) {
      // 清空之前选择的管网设备
      this.addDeviceFormData.networkDevice = "";
      this.addDeviceFormData.networkDeviceId = "";

      // 根据设备类型获取允许关联的管网设备类型
      this.updateAllowedNetworkDeviceTypes(deviceType);
    },

    // 更新允许关联的管网设备类型
    updateAllowedNetworkDeviceTypes(deviceType) {
      this.allowedNetworkDeviceTypes = [];

      if (!deviceType || !this.deviceMonitorRelations.length) {
        return;
      }

      // deviceType现在已经是数字ID了，不需要转换
      const deviceTypeId = deviceType;
      if (!deviceTypeId) {
        return;
      }

      // 查找当前电厂设备类型对应的管网设备类型
      // 根据API文档，需要根据devicetype字段匹配，然后收集monitorlabel字段
      const monitorLabels = [];
      this.deviceMonitorRelations.forEach(relation => {
        // 使用设备类型ID进行匹配
        if (relation.devicetype === deviceTypeId) {
          monitorLabels.push(relation.monitorlabel);
        }
      });

      this.allowedNetworkDeviceTypes = [...new Set(monitorLabels)]; // 去重
    },

    // 判断设备类型是否匹配
    isDeviceTypeMatch(deviceTypeId, deviceTypeName) {
      // 根据设备类型名称找到对应的ID
      const deviceTypeOption = this.deviceTypeOptions.find(
        option => option.text === deviceTypeName
      );

      if (!deviceTypeOption) {
        return false;
      }

      // 比较ID是否匹配
      return deviceTypeOption.id === deviceTypeId;
    },

    // 根据设备类型名称获取ID
    getDeviceTypeIdByName(deviceTypeName) {
      const option = this.deviceTypeOptions.find(
        opt => opt.text === deviceTypeName
      );
      return option ? option.id : null;
    },

    // 根据设备类型ID获取名称
    getDeviceTypeNameById(deviceTypeId) {
      const option = this.resourceTypeOptions.find(
        opt => opt.id == deviceTypeId
      );
      return option ? option.text : "--";
    },

    // 根据管网设备类型标识获取中文名称
    getNetworkDeviceTypeName(modelLabel) {
      if (!modelLabel) return $T("未知类型");

      // 从deviceTypeUtils.js中获取管网设备类型映射
      const { MODEL_LABEL_MAP } = require("@/utils/deviceTypeUtils");
      return MODEL_LABEL_MAP[modelLabel] || modelLabel || $T("未知类型");
    }
  },
  created() {
    this.loadDeviceMonitorRelations();
    // loadDevices由siteId的watch处理（immediate: true），避免重复调用
  },
  beforeDestroy() {
    // 清理定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }
    if (this.networkDeviceSearchTimer) {
      clearTimeout(this.networkDeviceSearchTimer);
    }
  }
};
</script>
<style scoped>
.device-management-container {
  width: 100%;
  height: 100%;
  background: var(--BG1);
  border-radius: var(--Ra);
  padding: var(--J4);
  margin-bottom: var(--J3);
}

.device-management {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 搜索栏样式 */
.search-bar {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--J2);
  margin-bottom: var(--J2);
}

.search-left {
  display: flex;
  align-items: center;
  gap: var(--J2);
  flex: 1;
}

.search-right {
  display: flex;
  align-items: center;
  gap: var(--J1);
}

.search-input {
  width: 240px;
  height: var(--J5);
}

.search-input .el-input__inner {
  height: var(--J5);
  line-height: var(--J5);
  padding: 1px 1px 1px var(--J1);
  border: 1px solid var(--B1);
  border-radius: var(--Ra);
  background: var(--BG1);
  font-size: var(--Aa);
  color: var(--T1);
  box-shadow: none;
}

.search-input .el-input__inner::placeholder {
  color: var(--T4);
  font-size: var(--Aa);
}

.search-input .el-input__inner:focus {
  border-color: var(--ZS);
  box-shadow: none;
}

.search-input .el-input__prefix {
  left: var(--J1);
}

.search-input .el-input__prefix .el-input__icon {
  color: var(--T4);
  font-size: var(--J2);
  line-height: var(--J5);
}

.category-select {
  width: 160px;
}

.category-select .el-input__inner {
  border: 1px solid var(--B1);
  border-radius: var(--Ra);
  background: var(--BG1);
  color: var(--T1);
  font-size: var(--Aa);
  padding: 0 var(--J1);
  height: var(--J5);
  line-height: var(--J5);
}

.category-select .el-input__inner:focus {
  border-color: var(--ZS);
  box-shadow: none;
}

.category-select .el-input__suffix {
  right: 0;
}

.category-select .el-input__suffix .el-input__icon {
  color: var(--T3);
  font-size: var(--Ab);
}

.add-device-btn {
  height: var(--J5);
  padding: 0 var(--J2);
  border: none;
  border-radius: var(--Ra);
  background: var(--ZS);
  color: var(--T5);
  font-size: var(--Aa);
  font-weight: 400;
  line-height: var(--J3);
}

.add-device-btn:hover,
.add-device-btn:focus {
  background: var(--ZS);
  opacity: 0.8;
}

.batch-delete-btn {
  height: var(--J5);
  padding: 0 var(--J2);
  border: 1px solid var(--Sta3);
  border-radius: var(--Ra);
  background: var(--Sta3);
  color: var(--T5);
  font-size: var(--Aa);
  font-weight: 400;
  line-height: var(--J3);
  margin-right: var(--J2);
}

.batch-delete-btn:hover,
.batch-delete-btn:focus {
  background: var(--Sta3);
  border-color: var(--Sta3);
  opacity: 0.8;
}

.batch-delete-btn:disabled {
  background: var(--B2);
  border-color: var(--B2);
  color: var(--T3);
  cursor: not-allowed;
}

.batch-delete-btn:disabled:hover {
  background: var(--B2);
  border-color: var(--B2);
  opacity: 1;
}

.flex1 {
  flex: 1;
  min-width: 0;
  min-height: 0;
}

.flex1 .el-table {
  background: transparent;
  border: none;
}

.flex1 .el-table th {
  background: var(--BG);
  border-bottom: 1px solid var(--B2);
  color: var(--T3);
  font-size: var(--Ab);
  font-weight: 400;
  line-height: var(--J2);
  padding: var(--J1) var(--J3);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.flex1 .el-table th .cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.flex1 .el-table td {
  border-bottom: 1px solid var(--B2);
  color: var(--T1);
  font-size: var(--Aa);
  font-weight: 400;
  line-height: var(--J3);
  padding: var(--J1) var(--J3);
}

/* 设备状态样式 */
.status-normal {
  color: var(--Sta1);
  background: var(--BG4);
  padding: var(--J0) var(--J1);
  border-radius: var(--Ra);
  font-size: var(--Ab);
  font-weight: 500;
}

.status-error {
  color: var(--Sta3);
  background: var(--BG4);
  padding: var(--J0) var(--J1);
  border-radius: var(--Ra);
  font-size: var(--Ab);
  font-weight: 500;
}

.status-maintenance {
  color: var(--F2);
  background: var(--BG4);
  padding: var(--J0) var(--J1);
  border-radius: var(--Ra);
  font-size: var(--Ab);
  font-weight: 500;
}
/* 操作链接样式 */
.action-link {
  font-size: var(--Aa);
  cursor: pointer;
  margin-right: var(--J2);
  text-decoration: none;
  transition: opacity 0.2s;
}

.detail-link {
  color: var(--ZS);
}

.detail-link:hover {
  opacity: 0.8;
}

.edit-link {
  color: var(--ZS);
}

.edit-link:hover {
  opacity: 0.8;
}

.delete-link {
  color: var(--Sta3);
}

.delete-link:hover {
  opacity: 0.8;
}

/* 分页样式 */
.pagination-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--J2);
  margin-top: var(--J2);
  font-size: var(--Aa);
  color: var(--T1);
}

.total-info {
  font-size: var(--Aa);
  color: var(--T1);
}

.total-info .total-highlight {
  color: var(--ZS);
  font-weight: 500;
}

.custom-pagination >>> .el-pagination {
  display: flex;
  align-items: center;
  gap: var(--J1);
}

.custom-pagination >>> .el-pager li {
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  border-radius: var(--Ra);
  border: 1px solid var(--B2);
  background: var(--BG1);
  color: var(--T1);
  font-size: var(--Aa);
  margin: 0;
}

.custom-pagination >>> .el-pager li.active {
  background: var(--BG2);
  color: var(--T1);
  border-color: var(--B2);
}

.custom-pagination >>> .btn-prev,
.custom-pagination >>> .btn-next {
  width: 32px;
  height: 32px;
  line-height: 32px;
  border-radius: var(--Ra);
  border: 1px solid var(--B2);
  background: var(--BG1);
  color: var(--T1);
}

.custom-pagination >>> .el-pagination__jump {
  margin-left: var(--J2);
  color: var(--T1);
  font-size: var(--Aa);
}

.custom-pagination >>> .el-pagination__jump .el-input__inner {
  width: 48px;
  height: 32px;
  border: 1px solid var(--B2);
  border-radius: var(--Ra);
  text-align: center;
}

/* 新增设备弹窗样式 - 统一VppDeviceManagement样式 */
.add-device-dialog >>> .el-dialog {
  background: var(--BG1);
  border-radius: var(--Ra1);
  overflow: hidden;
}

.add-device-dialog >>> .el-dialog__header {
  background: var(--BG1);
  padding: var(--J3) var(--J4) var(--J2) var(--J4);
  position: relative;
}

.add-device-dialog >>> .el-dialog__title {
  color: var(--T1);
  font-size: var(--Ac);
  font-weight: 600;
}

.add-device-dialog >>> .el-dialog__headerbtn {
  position: absolute;
  top: var(--J3);
  right: var(--J3);
}

.add-device-dialog >>> .el-dialog__headerbtn .el-dialog__close {
  font-size: var(--Ac);
  color: var(--T3);
}

.add-device-dialog >>> .el-dialog__body {
  background: var(--BG1);
  padding: var(--J1) var(--J4) var(--J3) var(--J4);
}

.add-device-dialog >>> .el-dialog__footer {
  background: var(--BG1);
  padding: var(--J2) var(--J4) var(--J3) var(--J4);
  text-align: right;
}

.dialog-content {
  background: var(--BG1);
}

.device-form >>> .el-form-item__label {
  color: var(--T1);
  font-size: var(--Aa);
  font-weight: 400;
  line-height: var(--J3);
  padding-bottom: var(--J1);
}

.device-form >>> .el-form-item__label::before {
  content: "*";
  color: var(--Sta3);
  margin-right: var(--J0);
}

.device-form >>> .el-input__inner {
  background: var(--BG1);
  border: 1px solid var(--B1);
  color: var(--T1);
  font-size: var(--Aa);
  border-radius: var(--Ra);
  height: 40px;
  line-height: 40px;
}

.device-form >>> .el-input__inner:focus {
  border-color: var(--ZS);
}

.device-form >>> .el-input__inner::placeholder {
  color: var(--T4);
}

.device-form >>> .el-select .el-input__inner {
  cursor: pointer;
}

.network-device-input {
  position: relative;
}

.network-device-icon {
  position: absolute;
  right: var(--J1);
  top: 50%;
  transform: translateY(-50%);
  font-size: var(--J2);
  color: var(--T3);
  cursor: pointer;
  z-index: 10;
}

/* 管网设备选择弹窗样式 - 统一VppDeviceManagement样式 */
.network-device-dialog >>> .el-dialog__wrapper {
  z-index: 3000 !important;
}

.network-device-dialog .el-dialog__body {
  padding: var(--J3);
}

.network-device-content {
  width: 100%;
}

.network-device-content .search-bar {
  margin-bottom: var(--J2);
}

.network-device-content .search-input {
  width: 300px;
}

.network-device-content .pagination-container {
  margin-top: var(--J2);
  text-align: right;
}

.dialog-footer {
  text-align: right;
}

.detail-content {
  /* padding: var(--J4); */
  background: var(--BG1);
}
</style>
